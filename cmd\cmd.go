package main

import (
	"fmt"
	"log"
	"os"

	"frp-panel/pkg/config"
	"frp-panel/pkg/database"
	"frp-panel/pkg/server"

	"github.com/spf13/cobra"
)

const version = "0.0.3"

var (
	showVersion bool
	configFile  string

	// 兼容旧的命令行参数
	bindAddr    string
	databaseDSN string
	authAPIURL  string
)

func init() {
	rootCmd.PersistentFlags().BoolVarP(&showVersion, "version", "v", false, "version")
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "", "config file path")

	// 兼容旧的命令行参数
	rootCmd.PersistentFlags().StringVarP(&bindAddr, "bind_addr", "l", "", "bind address (deprecated, use config file)")
	rootCmd.PersistentFlags().StringVarP(&databaseDSN, "database", "d", "", "database DSN (deprecated, use config file)")
	rootCmd.PersistentFlags().StringVarP(&authAPIURL, "auth_api", "a", "", "authentication API URL (deprecated, use config file)")
}

var rootCmd = &cobra.Command{
	Use:   "frp-panel",
	Short: "frp-panel is the server plugin of frp to support multiple users.",
	RunE: func(cmd *cobra.Command, args []string) error {
		if showVersion {
			fmt.Println(version)
			return nil
		}

		var cfg *config.Config
		var err error

		if configFile != "" {
			// 使用配置文件
			cfg, err = config.LoadConfig(configFile)
			if err != nil {
				return fmt.Errorf("failed to load config: %v", err)
			}
		} else {
			// 使用命令行参数（向后兼容）
			cfg = &config.Config{
				Server: config.ServerConfig{
					BindAddress: "127.0.0.1",
					Port:        7200,
				},
				Database: config.DatabaseConfig{
					Type: "mysql",
					DSN:  "root:123456@tcp(127.0.0.1:3305)/frp?charset=utf8mb4&parseTime=True&loc=Local",
				},
				Auth: config.AuthConfig{
					APIURL: "http://localhost:8080",
				},
				Admin: config.AdminConfig{
					BaseURL:  "http://127.0.0.1:7500/api",
					Username: "",
					Password: "",
				},
			}

			// 覆盖默认值
			if bindAddr != "" {
				cfg.Server.BindAddress = bindAddr
			}
			if databaseDSN != "" {
				cfg.Database.DSN = databaseDSN
			}
			if authAPIURL != "" {
				cfg.Auth.APIURL = authAPIURL
			}
		}

		s, err := server.New(cfg)
		if err != nil {
			return err
		}

		// Initialize default data (services and admin user)
		if err := database.Init(cfg.Database.DSN); err != nil {
			return fmt.Errorf("failed to initialize database: %v", err)
		}

		if err := InitializeDefaultData(); err != nil {
			log.Printf("Warning: failed to initialize default data: %v", err)
		}

		s.Run()
		return nil
	},
}

func Execute() {
	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}
